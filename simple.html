<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单网页</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .content {
            padding: 30px;
        }
        
        .content h2 {
            color: #333;
            border-bottom: 2px solid #ff6b6b;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .content p {
            color: #666;
            line-height: 1.8;
            margin-bottom: 15px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            background: #f8f9fa;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
            transition: transform 0.3s ease;
        }
        
        .feature-list li:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
        }
        
        .emoji {
            font-size: 1.5em;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="card">
        <div class="header">
            <h1>🌟 简单而美丽的网页 🌟</h1>
        </div>
        
        <div class="content">
            <h2>欢迎访问</h2>
            <p>这是一个简单但设计精美的HTML页面示例。它展示了现代网页设计的基本元素和技巧。</p>
            
            <h2>页面特色</h2>
            <ul class="feature-list">
                <li><span class="emoji">🎨</span> 渐变背景和现代化设计</li>
                <li><span class="emoji">📱</span> 响应式布局，适配各种设备</li>
                <li><span class="emoji">✨</span> 平滑的动画和过渡效果</li>
                <li><span class="emoji">🎯</span> 简洁清晰的内容结构</li>
                <li><span class="emoji">🌈</span> 丰富的视觉效果</li>
            </ul>
            
            <h2>技术特点</h2>
            <p>这个页面使用了以下技术：</p>
            <ul class="feature-list">
                <li>HTML5 语义化标签</li>
                <li>CSS3 渐变和动画</li>
                <li>Flexbox 布局</li>
                <li>响应式设计原则</li>
            </ul>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="#" class="btn" onclick="showMessage()">点击体验</a>
                <a href="https://developer.mozilla.org/zh-CN/" class="btn">学习更多</a>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 简单网页示例 | 用 ❤️ 制作</p>
        </div>
    </div>
    
    <script>
        function showMessage() {
            alert('🎉 欢迎来到这个美丽的网页！\n\n这是一个简单的JavaScript交互示例。');
        }
        
        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.8s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
            
            console.log('🚀 页面加载完成！欢迎来到简单而美丽的网页！');
        });
    </script>
</body>
</html>
