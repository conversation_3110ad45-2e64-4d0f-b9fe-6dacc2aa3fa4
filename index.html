<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单的HTML页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f4f4f4;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 10px;
        }
        
        h2 {
            color: #555;
            margin-top: 30px;
        }
        
        p {
            color: #666;
            margin-bottom: 15px;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 4px solid #ffc107;
            margin: 20px 0;
        }
        
        ul {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        
        .button:hover {
            background-color: #0056b3;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #888;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>欢迎来到我的简单网页</h1>
        
        <p>这是一个简单而美观的HTML页面示例。它包含了基本的HTML结构和CSS样式。</p>
        
        <div class="highlight">
            <strong>重要提示：</strong> 这个页面展示了HTML的基本元素和样式设计。
        </div>
        
        <h2>页面特色</h2>
        <ul>
            <li>响应式设计，适配不同屏幕尺寸</li>
            <li>现代化的CSS样式</li>
            <li>清晰的排版和布局</li>
            <li>优雅的颜色搭配</li>
            <li>交互式按钮效果</li>
        </ul>
        
        <h2>关于HTML</h2>
        <p>HTML（超文本标记语言）是创建网页的标准标记语言。它使用标签来描述网页的结构和内容。</p>
        
        <p>这个页面包含了常用的HTML元素：</p>
        <ul>
            <li><strong>标题</strong> - 使用 h1, h2 标签</li>
            <li><strong>段落</strong> - 使用 p 标签</li>
            <li><strong>列表</strong> - 使用 ul 和 li 标签</li>
            <li><strong>样式</strong> - 使用内嵌CSS</li>
        </ul>
        
        <a href="#" class="button" onclick="alert('你点击了按钮！')">点击我</a>
        
        <footer>
            <p>&copy; 2024 简单HTML页面示例</p>
        </footer>
    </div>
    
    <script>
        // 简单的JavaScript交互
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成！');
            
            // 为标题添加点击事件
            const title = document.querySelector('h1');
            title.addEventListener('click', function() {
                this.style.color = this.style.color === 'red' ? '#333' : 'red';
            });
        });
    </script>
</body>
</html>
